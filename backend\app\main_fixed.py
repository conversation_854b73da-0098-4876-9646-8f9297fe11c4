"""
Fixed Quantum Investment Platform Backend
Simplified and stable version with proper error handling
"""

import logging
import os
import sys
import random
import time
from contextlib import asynccontextmanager
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

import uvicorn
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine

# 预导入常用模块以减少运行时导入开销
import json
import asyncio

# 简单的内存缓存
class SimpleCache:
    def __init__(self, ttl_seconds=60):
        self.cache = {}
        self.ttl = ttl_seconds

    def get(self, key):
        if key in self.cache:
            data, timestamp = self.cache[key]
            if time.time() - timestamp < self.ttl:
                return data
            else:
                del self.cache[key]
        return None

    def set(self, key, value):
        self.cache[key] = (value, time.time())

    def clear(self):
        self.cache.clear()

# 全局缓存实例
api_cache = SimpleCache(ttl_seconds=30)  # 30秒缓存

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Database configuration
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite+aiosqlite:///./data/quantplatform.db")
logger.info(f"Using database: {DATABASE_URL}")

# Create database directory if it doesn't exist
db_path = Path("./data")
db_path.mkdir(exist_ok=True)

# Global database components
engine = None
async_session_maker = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifecycle management"""
    global engine, async_session_maker
    
    logger.info("Starting application...")
    
    try:
        # Initialize database
        engine = create_async_engine(
            DATABASE_URL,
            echo=False,
            connect_args={"check_same_thread": False} if "sqlite" in DATABASE_URL else {}
        )
        
        async_session_maker = async_sessionmaker(
            bind=engine,
            class_=AsyncSession,
            expire_on_commit=False
        )
        
        # Test database connection
        async with engine.begin() as conn:
            await conn.execute(text("SELECT 1"))
        logger.info("Database connection successful")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        # Continue without database - API will work with limited functionality
    
    yield
    
    # Cleanup
    logger.info("Shutting down application...")
    if engine:
        await engine.dispose()

# Create FastAPI application
app = FastAPI(
    title="Quantum Investment Platform API",
    description="High-performance quantitative trading platform backend",
    version="1.0.0",
    lifespan=lifespan
)

# 性能监控中间件
@app.middleware("http")
async def performance_middleware(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)

    # 添加连接优化头
    response.headers["Connection"] = "keep-alive"
    response.headers["Keep-Alive"] = "timeout=60, max=1000"

    # 记录慢请求
    if process_time > 0.1:  # 降低阈值到100ms
        logger.warning(f"慢请求: {request.url.path} 耗时 {process_time:.3f}s")

    return response

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ============= Include API Routes =============
# 暂时禁用v1路由导入，使用内置API端点确保稳定性
# 这样可以确保我们新添加的端点能够正常工作
logger.info("✅ 使用内置API端点（包含新增的市场数据端点）")

# ============= Health Check Endpoints =============

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "status": "running",
        "name": "Quantum Investment Platform API",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    health_status = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "api": "operational",
            "database": "unknown"
        }
    }
    
    # Check database
    if engine:
        try:
            async with engine.begin() as conn:
                await conn.execute(text("SELECT 1"))
            health_status["services"]["database"] = "operational"
        except:
            health_status["services"]["database"] = "degraded"
    
    return health_status

@app.get("/api/v1/health")
async def api_health():
    """API v1 health check"""
    return {"status": "ok", "version": "v1"}

# ============= Authentication Endpoints =============

@app.post("/api/v1/auth/login")
async def login(request: Request):
    """Login endpoint"""
    try:
        data = await request.json()
        username = data.get("username", "")
        password = data.get("password", "")
        
        # Simple mock authentication
        if username and password:
            return {
                "access_token": "mock-jwt-token-" + username,
                "token_type": "bearer",
                "user": {
                    "id": 1,
                    "username": username,
                    "email": f"{username}@example.com",
                    "roles": ["user"]
                }
            }
        else:
            raise HTTPException(status_code=401, detail="Invalid credentials")
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/auth/register")
async def register(request: Request):
    """Register endpoint"""
    try:
        data = await request.json()
        username = data.get("username", "")
        email = data.get("email", "")
        password = data.get("password", "")
        
        if username and email and password:
            return {
                "message": "User registered successfully",
                "user": {
                    "id": 1,
                    "username": username,
                    "email": email
                }
            }
        else:
            raise HTTPException(status_code=400, detail="Missing required fields")
    except Exception as e:
        logger.error(f"Registration error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/v1/auth/logout")
async def logout():
    """Logout endpoint"""
    return {"message": "Logged out successfully"}

# ============= Market Data Endpoints =============

@app.get("/api/v1/market/stocks")
async def get_stocks():
    """Get stock list from unified data service"""
    # 检查缓存
    cache_key = "market_stocks"
    cached_data = api_cache.get(cache_key)
    if cached_data:
        return cached_data

    try:
        # Import the unified data service
        from app.services.unified_data_service import get_stock_list
        stocks = await get_stock_list()
        result = {
            "data": stocks,
            "total": len(stocks)
        }
    except Exception as e:
        logger.error(f"Error getting stocks: {e}")
        # Fallback to static data
        result = {
            "data": [
                {"code": "000001", "name": "平安银行", "market": "SZ", "price": 12.5, "change": 0.5},
                {"code": "000002", "name": "万科A", "market": "SZ", "price": 15.3, "change": -0.3},
                {"code": "600000", "name": "浦发银行", "market": "SH", "price": 8.9, "change": 0.2},
                {"code": "600519", "name": "贵州茅台", "market": "SH", "price": 1680.0, "change": 20.0},
            ],
            "total": 4
        }

    # 缓存结果
    api_cache.set(cache_key, result)
    return result

@app.get("/api/v1/market/test")
async def test_endpoint():
    """Test endpoint to verify new endpoints are working"""
    return {"message": "New endpoint is working!", "timestamp": datetime.now().isoformat()}

@app.get("/api/v1/ping")
async def ping():
    """Ultra-fast ping endpoint for performance testing"""
    return {"ping": "pong"}

@app.get("/api/v1/fast")
async def fast_endpoint():
    """Minimal endpoint for performance testing"""
    return {"status": "ok", "time": time.time()}

@app.get("/api/v1/market/realtime/{symbol}")
async def get_realtime_data(symbol: str):
    """Get realtime market data"""
    # 检查缓存（较短的缓存时间，因为是实时数据）
    cache_key = f"realtime_{symbol}"
    cached_data = api_cache.get(cache_key)
    if cached_data:
        return cached_data

    base_price = 100.0
    result = {
        "symbol": symbol,
        "price": base_price + random.uniform(-5, 5),
        "open": base_price,
        "high": base_price + 5,
        "low": base_price - 5,
        "volume": random.randint(1000000, 10000000),
        "timestamp": datetime.now().isoformat()
    }

    # 缓存结果（10秒缓存，因为是实时数据）
    api_cache.set(cache_key, result)
    return result

@app.get("/api/v1/market/kline/{symbol}")
async def get_kline_data(symbol: str, period: str = "1d", limit: int = 100):
    """Get K-line data"""
    # 检查缓存
    cache_key = f"kline_{symbol}_{period}_{limit}"
    cached_data = api_cache.get(cache_key)
    if cached_data:
        return cached_data

    data = []
    base_price = 100.0

    for _ in range(limit):
        open_price = base_price + random.uniform(-5, 5)
        close_price = base_price + random.uniform(-5, 5)
        high_price = max(open_price, close_price) + random.uniform(0, 2)
        low_price = min(open_price, close_price) - random.uniform(0, 2)

        data.append({
            "timestamp": datetime.now().isoformat(),
            "open": open_price,
            "high": high_price,
            "low": low_price,
            "close": close_price,
            "volume": random.randint(100000, 1000000)
        })

    result = {"symbol": symbol, "period": period, "data": data}

    # 缓存结果
    api_cache.set(cache_key, result)
    return result

@app.get("/api/v1/market/quotes/realtime")
async def get_realtime_quotes(symbols: str):
    """Get realtime quotes for multiple symbols"""
    # 检查缓存
    cache_key = f"quotes_realtime_{symbols}"
    cached_data = api_cache.get(cache_key)
    if cached_data:
        return cached_data

    symbol_list = symbols.split(",")
    quotes = []

    # Mock data for common stocks
    mock_stocks = {
        "000001": {"name": "平安银行", "base_price": 12.5},
        "000002": {"name": "万科A", "base_price": 15.3},
        "600000": {"name": "浦发银行", "base_price": 8.9},
        "600519": {"name": "贵州茅台", "base_price": 1680.0},
        "000858": {"name": "五粮液", "base_price": 180.0},
        "002415": {"name": "海康威视", "base_price": 35.0}
    }

    for symbol in symbol_list:
        symbol = symbol.strip()
        if symbol in mock_stocks:
            stock = mock_stocks[symbol]
            base_price = stock["base_price"]
            change = random.uniform(-5, 5)
            current_price = base_price + change

            quotes.append({
                "symbol": symbol,
                "name": stock["name"],
                "price": round(current_price, 2),
                "change": round(change, 2),
                "change_percent": round((change / base_price) * 100, 2),
                "volume": random.randint(1000000, 50000000),
                "turnover": random.randint(100000000, 1000000000),
                "high": round(current_price + random.uniform(0, 2), 2),
                "low": round(current_price - random.uniform(0, 2), 2),
                "open": round(base_price + random.uniform(-2, 2), 2),
                "timestamp": datetime.now().isoformat()
            })
        else:
            # Default mock data for unknown symbols
            quotes.append({
                "symbol": symbol,
                "name": f"股票{symbol}",
                "price": round(random.uniform(10, 200), 2),
                "change": round(random.uniform(-10, 10), 2),
                "change_percent": round(random.uniform(-5, 5), 2),
                "volume": random.randint(1000000, 50000000),
                "turnover": random.randint(100000000, 1000000000),
                "high": round(random.uniform(10, 200), 2),
                "low": round(random.uniform(10, 200), 2),
                "open": round(random.uniform(10, 200), 2),
                "timestamp": datetime.now().isoformat()
            })

    result = {
        "success": True,
        "data": quotes,
        "count": len(quotes),
        "timestamp": datetime.now().isoformat()
    }

    # 缓存结果
    api_cache.set(cache_key, result)
    return result

@app.get("/api/v1/market/overview")
async def get_market_overview():
    """Get market overview data"""
    # 检查缓存
    cache_key = "market_overview"
    cached_data = api_cache.get(cache_key)
    if cached_data:
        return cached_data

    # Mock market indices
    indices = [
        {
            "code": "000001",
            "name": "上证指数",
            "value": round(3200 + random.uniform(-50, 50), 2),
            "change": round(random.uniform(-30, 30), 2),
            "change_percent": round(random.uniform(-1.5, 1.5), 2)
        },
        {
            "code": "399001",
            "name": "深证成指",
            "value": round(11000 + random.uniform(-200, 200), 2),
            "change": round(random.uniform(-100, 100), 2),
            "change_percent": round(random.uniform(-1.5, 1.5), 2)
        },
        {
            "code": "399006",
            "name": "创业板指",
            "value": round(2300 + random.uniform(-50, 50), 2),
            "change": round(random.uniform(-20, 20), 2),
            "change_percent": round(random.uniform(-2, 2), 2)
        }
    ]

    # Market statistics
    stats = {
        "total_stocks": 4800,
        "rising_stocks": random.randint(1500, 3000),
        "falling_stocks": random.randint(1000, 2500),
        "unchanged_stocks": random.randint(100, 500),
        "total_volume": random.randint(500000000000, 800000000000),
        "total_turnover": random.randint(600000000000, 900000000000)
    }

    # Hot sectors
    sectors = [
        {"name": "新能源", "change_percent": round(random.uniform(-3, 5), 2)},
        {"name": "半导体", "change_percent": round(random.uniform(-4, 6), 2)},
        {"name": "医药生物", "change_percent": round(random.uniform(-2, 3), 2)},
        {"name": "银行", "change_percent": round(random.uniform(-1, 2), 2)},
        {"name": "房地产", "change_percent": round(random.uniform(-3, 1), 2)}
    ]

    result = {
        "success": True,
        "data": {
            "indices": indices,
            "statistics": stats,
            "hot_sectors": sectors,
            "market_status": "trading",  # trading, closed, pre_market, after_market
            "trading_day": datetime.now().strftime("%Y-%m-%d"),
            "last_update": datetime.now().isoformat()
        },
        "timestamp": datetime.now().isoformat()
    }

    # 缓存结果
    api_cache.set(cache_key, result)
    return result

# ============= Trading Endpoints =============

@app.get("/api/v1/trading/positions")
async def get_positions():
    """Get trading positions"""
    return {
        "positions": [
            {
                "id": 1,
                "symbol": "000001",
                "name": "平安银行",
                "quantity": 1000,
                "avg_price": 12.0,
                "current_price": 12.5,
                "pnl": 500.0,
                "pnl_ratio": 0.042
            }
        ],
        "total_value": 12500.0,
        "total_pnl": 500.0
    }

@app.post("/api/v1/trading/orders")
async def create_order(request: Request):
    """Create trading order"""
    try:
        data = await request.json()
        return {
            "order_id": "ORD" + datetime.now().strftime("%Y%m%d%H%M%S"),
            "status": "submitted",
            "symbol": data.get("symbol"),
            "quantity": data.get("quantity"),
            "price": data.get("price"),
            "side": data.get("side"),
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Order creation error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/trading/orders")
async def get_orders():
    """Get trading orders"""
    return {
        "orders": [
            {
                "order_id": "ORD20250101120000",
                "symbol": "000001",
                "quantity": 1000,
                "price": 12.5,
                "side": "buy",
                "status": "filled",
                "timestamp": datetime.now().isoformat()
            }
        ],
        "total": 1
    }

# ============= Strategy Endpoints =============

@app.get("/api/v1/strategies")
async def get_strategies():
    """Get strategy list"""
    return {
        "strategies": [
            {
                "id": 1,
                "name": "Moving Average Cross",
                "type": "trend_following",
                "status": "active",
                "performance": {
                    "total_return": 0.15,
                    "sharpe_ratio": 1.2,
                    "max_drawdown": -0.08
                }
            },
            {
                "id": 2,
                "name": "Mean Reversion",
                "type": "mean_reversion",
                "status": "inactive",
                "performance": {
                    "total_return": 0.08,
                    "sharpe_ratio": 0.9,
                    "max_drawdown": -0.05
                }
            }
        ],
        "total": 2
    }

@app.post("/api/v1/strategies")
async def create_strategy(request: Request):
    """Create new strategy"""
    try:
        data = await request.json()
        return {
            "id": 3,
            "name": data.get("name", "New Strategy"),
            "type": data.get("type", "custom"),
            "status": "created",
            "created_at": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Strategy creation error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ============= Backtest Endpoints =============

@app.post("/api/v1/backtest/run")
async def run_backtest(request: Request):
    """Run strategy backtest"""
    try:
        data = await request.json()
        return {
            "backtest_id": "BT" + datetime.now().strftime("%Y%m%d%H%M%S"),
            "strategy_id": data.get("strategy_id"),
            "status": "running",
            "progress": 0,
            "start_date": data.get("start_date"),
            "end_date": data.get("end_date"),
            "initial_capital": data.get("initial_capital", 100000)
        }
    except Exception as e:
        logger.error(f"Backtest error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/v1/backtest/results/{backtest_id}")
async def get_backtest_results(backtest_id: str):
    """Get backtest results"""
    return {
        "backtest_id": backtest_id,
        "status": "completed",
        "metrics": {
            "total_return": 0.25,
            "annualized_return": 0.12,
            "sharpe_ratio": 1.5,
            "max_drawdown": -0.10,
            "win_rate": 0.65,
            "profit_factor": 1.8
        },
        "trades": 150,
        "winning_trades": 98,
        "losing_trades": 52
    }

# ============= Risk Management Endpoints =============

@app.get("/api/v1/risk/metrics")
async def get_risk_metrics():
    """Get risk metrics"""
    return {
        "portfolio_risk": {
            "var_95": -5000,
            "var_99": -8000,
            "expected_shortfall": -10000,
            "beta": 1.2,
            "correlation_with_market": 0.85
        },
        "position_risks": [
            {
                "symbol": "000001",
                "var_95": -500,
                "concentration": 0.1,
                "liquidity_risk": "low"
            }
        ]
    }

@app.get("/api/v1/risk/limits")
async def get_risk_limits():
    """Get risk limits"""
    return {
        "limits": {
            "max_position_size": 0.2,
            "max_leverage": 2.0,
            "max_drawdown": 0.15,
            "daily_loss_limit": 0.05
        },
        "current_usage": {
            "position_size": 0.1,
            "leverage": 1.0,
            "drawdown": 0.03,
            "daily_loss": 0.01
        }
    }

# ============= WebSocket Endpoint with Heartbeat =============

from fastapi import WebSocket, WebSocketDisconnect
import asyncio
import json

@app.websocket("/api/v1/ws/market")
async def websocket_market(websocket: WebSocket):
    """WebSocket for market data streaming with heartbeat"""
    await websocket.accept()
    logger.info("WebSocket connection established")
    
    async def send_heartbeat():
        """Send heartbeat every 30 seconds"""
        while True:
            try:
                await asyncio.sleep(30)
                await websocket.send_json({
                    "type": "heartbeat",
                    "timestamp": datetime.now().isoformat()
                })
            except:
                break
    
    async def send_market_data():
        """Send market data every second"""
        import random
        while True:
            try:
                await asyncio.sleep(1)
                await websocket.send_json({
                    "type": "market_data",
                    "data": {
                        "symbol": "000001",
                        "price": 100 + random.uniform(-5, 5),
                        "volume": random.randint(100000, 1000000),
                        "timestamp": datetime.now().isoformat()
                    }
                })
            except:
                break
    
    # Create background tasks
    heartbeat_task = asyncio.create_task(send_heartbeat())
    market_task = asyncio.create_task(send_market_data())
    
    try:
        while True:
            # Receive and handle client messages
            data = await websocket.receive_text()
            msg = json.loads(data) if data else {}
            
            # Handle different message types
            if msg.get("type") == "ping":
                await websocket.send_json({
                    "type": "pong",
                    "timestamp": datetime.now().isoformat()
                })
            elif msg.get("type") == "subscribe":
                symbol = msg.get("symbol")
                logger.info(f"Client subscribed to {symbol}")
            
    except WebSocketDisconnect:
        logger.info("WebSocket disconnected")
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
    finally:
        # Cancel background tasks
        heartbeat_task.cancel()
        market_task.cancel()
        await websocket.close()

# ============= Error Handlers =============

@app.exception_handler(404)
async def not_found_handler(request: Request, exc):
    return JSONResponse(
        status_code=404,
        content={"detail": "Endpoint not found"}
    )

@app.exception_handler(500)
async def internal_error_handler(request: Request, exc):
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )

# ============= Main Entry Point =============

if __name__ == "__main__":
    # Run with uvicorn - 优化配置以减少延迟
    uvicorn.run(
        "main_fixed:app",
        host="127.0.0.1",  # 使用IPv4 localhost而不是0.0.0.0
        port=8000,
        reload=True,
        log_level="info",
        # 性能优化配置
        loop="asyncio",  # 使用asyncio事件循环
        access_log=False,  # 禁用访问日志以提高性能
        # 连接优化
        backlog=2048,  # 增加连接队列
        limit_concurrency=1000,  # 限制并发连接数
        limit_max_requests=10000,  # 每个worker的最大请求数
        timeout_keep_alive=60,  # Keep-alive超时
    )