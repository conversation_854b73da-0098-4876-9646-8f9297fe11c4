<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化投资平台</title>

    <!-- 性能优化：DNS预解析 -->
    <link rel="dns-prefetch" href="//localhost:8000">
    <link rel="dns-prefetch" href="//cdn.jsdelivr.net">

    <!-- 性能优化：预连接 -->
    <link rel="preconnect" href="//localhost:8000">

    <!-- 性能优化：预加载关键资源 - 移除不必要的预加载 -->
    <!-- Vite会自动处理模块加载，不需要手动预加载 -->

    <!-- 安全头：内容安全策略 - 临时放宽以便调试 -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; connect-src 'self' ws: http: https:;">

    <!-- 注意：X-Frame-Options等安全头应该在服务器端设置，而不是在meta标签中 -->
    <!-- 这些meta标签会导致控制台警告，已移除 -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">

    <!-- 缓存策略优化 -->
    <meta http-equiv="Cache-Control" content="public, max-age=31536000">
    <meta name="format-detection" content="telephone=no">

    <!-- PWA支持 -->
    <meta name="theme-color" content="#409EFF">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">

    <!-- 性能优化：关键CSS内联 -->
    <style>
      /* 关键CSS - 首屏渲染优化 */
      #app {
        width: 100%;
        height: 100vh;
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }

      /* 加载动画 */
      .app-loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #e4e7ed;
        border-top: 4px solid #409eff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div id="app">
      <!-- 首屏加载动画 -->
      <div class="app-loading">
        <div class="loading-spinner"></div>
      </div>
    </div>

    <!-- 直接嵌入JavaScript测试 -->
    <script>
      console.log('🚀 内联JavaScript开始执行')

      // 等待DOM加载
      document.addEventListener('DOMContentLoaded', function() {
        console.log('📍 DOM加载完成')

        const app = document.getElementById('app')
        if (app) {
          console.log('✅ 找到app元素')
          app.innerHTML = `
            <div style="padding: 40px; text-align: center; font-family: Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh;">
              <div style="background: white; padding: 40px; border-radius: 16px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); max-width: 600px; margin: 0 auto;">
                <h1 style="color: #2c3e50; margin-bottom: 20px;">🚀 量化投资平台</h1>
                <p style="color: #6b7280; margin-bottom: 30px;">JavaScript已成功执行！</p>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 30px;">
                  <div style="padding: 20px; background: #f8fafc; border-radius: 8px; border-left: 4px solid #3b82f6;">
                    <h3 style="color: #2c3e50; margin-bottom: 8px;">📊 仪表盘</h3>
                    <p style="color: #6b7280; font-size: 14px;">投资数据概览</p>
                  </div>
                  <div style="padding: 20px; background: #f8fafc; border-radius: 8px; border-left: 4px solid #10b981;">
                    <h3 style="color: #2c3e50; margin-bottom: 8px;">📈 市场行情</h3>
                    <p style="color: #6b7280; font-size: 14px;">实时市场数据</p>
                  </div>
                  <div style="padding: 20px; background: #f8fafc; border-radius: 8px; border-left: 4px solid #f59e0b;">
                    <h3 style="color: #2c3e50; margin-bottom: 8px;">💰 智能交易</h3>
                    <p style="color: #6b7280; font-size: 14px;">自动化交易系统</p>
                  </div>
                  <div style="padding: 20px; background: #f8fafc; border-radius: 8px; border-left: 4px solid #8b5cf6;">
                    <h3 style="color: #2c3e50; margin-bottom: 8px;">🧠 策略研发</h3>
                    <p style="color: #6b7280; font-size: 14px;">量化策略开发</p>
                  </div>
                </div>
                <button onclick="alert('Vue应用功能开发中...')" style="padding: 12px 24px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 16px;">
                  🔄 启动完整应用
                </button>
              </div>
            </div>
          `
          console.log('✅ 页面内容已更新')
        } else {
          console.error('❌ 未找到app元素')
        }
      })

      console.log('✅ 内联JavaScript执行完成')
    </script>

    <!-- 备用：模块加载 -->
    <script type="module" src="/src/main.js"></script>

    <!-- 性能监控脚本 -->
    <script>
      // 首屏性能监控
      window.addEventListener('load', function() {
        // 移除加载动画
        const loading = document.querySelector('.app-loading');
        if (loading) {
          loading.style.opacity = '0';
          setTimeout(() => loading.remove(), 300);
        }

        // 性能数据收集
        if ('performance' in window && performance.getEntriesByType) {
          try {
            const perfData = performance.getEntriesByType('navigation')[0];
            if (perfData && perfData.loadEventEnd && perfData.navigationStart) {
              const loadTime = perfData.loadEventEnd - perfData.navigationStart;
              if (loadTime > 0) {
                console.log('📊 页面加载时间:', loadTime + 'ms');

                // 如果加载时间超过3秒，记录警告
                if (loadTime > 3000) {
                  console.warn('⚠️ 页面加载较慢，建议优化');
                }
              }
            }
          } catch (error) {
            console.log('📊 性能监控初始化失败:', error.message);
          }
        }
      });

      // 错误监控
      window.addEventListener('error', function(e) {
        console.error('🚨 页面错误:', e.error || e.message);
      });

      // 未处理的Promise错误
      window.addEventListener('unhandledrejection', function(e) {
        console.error('🚨 未处理的Promise错误:', e.reason);
      });
    </script>
  </body>
</html>
