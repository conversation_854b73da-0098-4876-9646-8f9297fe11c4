<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化投资平台</title>

    <!-- 性能优化：DNS预解析 -->
    <link rel="dns-prefetch" href="//localhost:8000">
    <link rel="dns-prefetch" href="//cdn.jsdelivr.net">

    <!-- 性能优化：预连接 -->
    <link rel="preconnect" href="//localhost:8000">

    <!-- 性能优化：预加载关键资源 - 移除不必要的预加载 -->
    <!-- Vite会自动处理模块加载，不需要手动预加载 -->

    <!-- 安全头：内容安全策略 - 临时放宽以便调试 -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; connect-src 'self' ws: http: https:;">

    <!-- 注意：X-Frame-Options等安全头应该在服务器端设置，而不是在meta标签中 -->
    <!-- 这些meta标签会导致控制台警告，已移除 -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">

    <!-- 缓存策略优化 -->
    <meta http-equiv="Cache-Control" content="public, max-age=31536000">
    <meta name="format-detection" content="telephone=no">

    <!-- PWA支持 -->
    <meta name="theme-color" content="#409EFF">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">

    <style>
      #app { width: 100%; min-height: 100vh; }
    </style>
  </head>
  <body>
    <div id="app">
    </div>
    
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
