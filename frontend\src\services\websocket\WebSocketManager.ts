import { EventEmitter } from 'mitt'
import mitt from 'mitt'

export interface WebSocketConfig {
  url: string
  protocols?: string | string[]
  reconnect?: boolean
  reconnectInterval?: number
  reconnectMaxAttempts?: number
  heartbeatInterval?: number
  messageQueueSize?: number
  binaryType?: BinaryType
}

export interface WebSocketMessage {
  type: string
  data: any
  timestamp: number
}

export enum WebSocketState {
  CONNECTING = 0,
  OPEN = 1,
  CLOSING = 2,
  CLOSED = 3,
  RECONNECTING = 4,
  ERROR = 5
}

type Events = {
  open: Event
  close: CloseEvent
  error: Event
  message: WebSocketMessage
  stateChange: WebSocketState
  reconnecting: number
  reconnected: void
}

export class WebSocketManager {
  private ws: WebSocket | null = null
  private config: Required<WebSocketConfig>
  private emitter: EventEmitter<Events>
  private reconnectAttempts = 0
  private reconnectTimer: number | null = null
  private heartbeatTimer: number | null = null
  private messageQueue: WebSocketMessage[] = []
  private state: WebSocketState = WebSocketState.CLOSED
  private isIntentionallyClosed = false
  private lastPingTime = 0
  private connectionQuality: 'excellent' | 'good' | 'poor' = 'good'

  constructor(config: WebSocketConfig) {
    this.config = {
      url: config.url,
      protocols: config.protocols,
      reconnect: config.reconnect ?? true,
      reconnectInterval: config.reconnectInterval ?? 3000,
      reconnectMaxAttempts: config.reconnectMaxAttempts ?? 10,
      heartbeatInterval: config.heartbeatInterval ?? 30000,
      messageQueueSize: config.messageQueueSize ?? 100,
      binaryType: config.binaryType ?? 'blob'
    }
    
    this.emitter = mitt<Events>()
  }

  // 连接WebSocket
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        resolve()
        return
      }

      this.isIntentionallyClosed = false
      this.setState(WebSocketState.CONNECTING)

      try {
        this.ws = new WebSocket(this.config.url, this.config.protocols)
        this.ws.binaryType = this.config.binaryType

        // 连接成功
        this.ws.onopen = (event) => {
          console.log('WebSocket connected:', this.config.url)
          this.setState(WebSocketState.OPEN)
          this.reconnectAttempts = 0
          this.startHeartbeat()
          this.flushMessageQueue()
          this.emitter.emit('open', event)
          
          if (this.reconnectAttempts > 0) {
            this.emitter.emit('reconnected')
          }
          
          resolve()
        }

        // 接收消息
        this.ws.onmessage = (event) => {
          this.handleMessage(event)
        }

        // 连接关闭
        this.ws.onclose = (event) => {
          console.log('WebSocket closed:', event.code, event.reason)
          this.setState(WebSocketState.CLOSED)
          this.stopHeartbeat()
          this.emitter.emit('close', event)

          // 自动重连
          if (!this.isIntentionallyClosed && this.config.reconnect) {
            this.scheduleReconnect()
          }
        }

        // 连接错误
        this.ws.onerror = (event) => {
          console.error('WebSocket error:', event)
          this.setState(WebSocketState.ERROR)
          this.emitter.emit('error', event)
          reject(new Error('WebSocket connection failed'))
        }

        // 设置连接超时
        const connectTimeout = setTimeout(() => {
          if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
            this.ws.close()
            reject(new Error('WebSocket connection timeout'))
          }
        }, 10000)

        this.ws.addEventListener('open', () => {
          clearTimeout(connectTimeout)
        })

      } catch (error) {
        this.setState(WebSocketState.ERROR)
        reject(error)
      }
    })
  }

  // 断开连接
  disconnect(): void {
    this.isIntentionallyClosed = true
    this.cancelReconnect()
    this.stopHeartbeat()
    
    if (this.ws) {
      this.setState(WebSocketState.CLOSING)
      this.ws.close(1000, 'Client disconnect')
      this.ws = null
    }
  }

  // 发送消息（带缓存队列）
  send(type: string, data: any): boolean {
    const message: WebSocketMessage = {
      type,
      data,
      timestamp: Date.now()
    }

    if (this.isConnected()) {
      try {
        this.ws!.send(JSON.stringify(message))
        return true
      } catch (error) {
        console.error('Failed to send message:', error)
        this.queueMessage(message)
        return false
      }
    } else {
      // 连接未就绪，加入队列
      this.queueMessage(message)
      return false
    }
  }

  // 订阅事件
  on<K extends keyof Events>(event: K, handler: (data: Events[K]) => void): void {
    this.emitter.on(event, handler as any)
  }

  // 取消订阅
  off<K extends keyof Events>(event: K, handler: (data: Events[K]) => void): void {
    this.emitter.off(event, handler as any)
  }

  // 获取连接状态
  getState(): WebSocketState {
    return this.state
  }

  // 获取连接质量
  getConnectionQuality(): 'excellent' | 'good' | 'poor' {
    return this.connectionQuality
  }

  // 是否已连接
  isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN
  }

  // 私有方法：处理消息
  private handleMessage(event: MessageEvent): void {
    try {
      const message = this.parseMessage(event.data)
      
      // 处理心跳响应
      if (message.type === 'pong') {
        this.updateConnectionQuality()
        return
      }

      this.emitter.emit('message', message)
    } catch (error) {
      console.error('Failed to parse message:', error)
    }
  }

  // 私有方法：解析消息
  private parseMessage(data: any): WebSocketMessage {
    if (typeof data === 'string') {
      try {
        const parsed = JSON.parse(data)
        return {
          type: parsed.type || 'unknown',
          data: parsed.data || parsed,
          timestamp: parsed.timestamp || Date.now()
        }
      } catch {
        return {
          type: 'text',
          data: data,
          timestamp: Date.now()
        }
      }
    }
    
    return {
      type: 'binary',
      data: data,
      timestamp: Date.now()
    }
  }

  // 私有方法：设置状态
  private setState(state: WebSocketState): void {
    if (this.state !== state) {
      this.state = state
      this.emitter.emit('stateChange', state)
    }
  }

  // 私有方法：心跳机制
  private startHeartbeat(): void {
    this.stopHeartbeat()
    
    this.heartbeatTimer = window.setInterval(() => {
      if (this.isConnected()) {
        this.lastPingTime = Date.now()
        this.send('ping', { timestamp: this.lastPingTime })
      }
    }, this.config.heartbeatInterval)
  }

  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  // 私有方法：更新连接质量
  private updateConnectionQuality(): void {
    const latency = Date.now() - this.lastPingTime
    
    if (latency < 100) {
      this.connectionQuality = 'excellent'
    } else if (latency < 300) {
      this.connectionQuality = 'good'
    } else {
      this.connectionQuality = 'poor'
    }
  }

  // 私有方法：消息队列管理
  private queueMessage(message: WebSocketMessage): void {
    this.messageQueue.push(message)
    
    // 限制队列大小
    if (this.messageQueue.length > this.config.messageQueueSize) {
      this.messageQueue.shift()
    }
  }

  private flushMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.isConnected()) {
      const message = this.messageQueue.shift()!
      try {
        this.ws!.send(JSON.stringify(message))
      } catch (error) {
        console.error('Failed to flush message:', error)
        this.messageQueue.unshift(message)
        break
      }
    }
  }

  // 私有方法：重连机制
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.config.reconnectMaxAttempts) {
      console.error('Max reconnection attempts reached')
      return
    }

    this.reconnectAttempts++
    this.setState(WebSocketState.RECONNECTING)
    this.emitter.emit('reconnecting', this.reconnectAttempts)

    const delay = Math.min(
      this.config.reconnectInterval * Math.pow(1.5, this.reconnectAttempts - 1),
      30000
    )

    console.log(`Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts})`)

    this.reconnectTimer = window.setTimeout(() => {
      this.connect().catch(error => {
        console.error('Reconnection failed:', error)
      })
    }, delay)
  }

  private cancelReconnect(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }

  // 清理资源
  destroy(): void {
    this.disconnect()
    this.emitter.all.clear()
    this.messageQueue = []
  }
}

// 单例管理器
class WebSocketPool {
  private connections: Map<string, WebSocketManager> = new Map()

  getConnection(url: string, config?: Partial<WebSocketConfig>): WebSocketManager {
    if (!this.connections.has(url)) {
      this.connections.set(url, new WebSocketManager({ url, ...config }))
    }
    return this.connections.get(url)!
  }

  removeConnection(url: string): void {
    const connection = this.connections.get(url)
    if (connection) {
      connection.destroy()
      this.connections.delete(url)
    }
  }

  clear(): void {
    this.connections.forEach(connection => connection.destroy())
    this.connections.clear()
  }
}

export const wsPool = new WebSocketPool()